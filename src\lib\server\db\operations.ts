import { eq, and, gte, lt, desc, asc } from 'drizzle-orm';
import { db } from './index.js';
import { users, sessions, otpRequests, emailLimits, officeFlowLinks, categories, tasks, taskReminders, emailLogs } from './schema.js';
import type { User, NewUser, Session, NewSession, OtpRequest, NewOtpRequest, EmailLimit, NewEmailLimit, OfficeFlowLink, NewOfficeFlowLink, Category, NewCategory, Task, NewTask, TaskReminder, NewTaskReminder, EmailLog, NewEmailLog } from './schema.js';
import { v4 as uuidv4 } from 'uuid';

// User operations
export async function createUser(userData: Omit<NewUser, 'id' | 'createdAt' | 'updatedAt'>): Promise<User> {
  const [user] = await db.insert(users).values(userData).returning();
  return user;
}

export async function getUserByEmail(email: string): Promise<User | null> {
  const [user] = await db.select().from(users).where(eq(users.email, email));
  return user || null;
}

export async function getUserById(id: string): Promise<User | null> {
  const [user] = await db.select().from(users).where(eq(users.id, id));
  return user || null;
}

export async function updateUserVerification(id: string, isVerified: boolean): Promise<void> {
  await db.update(users).set({ isVerified, updatedAt: new Date() }).where(eq(users.id, id));
}

export async function updateUserPassword(id: string, hashedPassword: string): Promise<void> {
  await db.update(users).set({ password: hashedPassword, updatedAt: new Date() }).where(eq(users.id, id));
}

export async function updateUserSettings(id: string, settings: Partial<Pick<User, 'timezone' | 'emailFrequencyDays' | 'emailPreviewDays' | 'emailRemindersEnabled'>>): Promise<void> {
  await db.update(users).set({ ...settings, updatedAt: new Date() }).where(eq(users.id, id));
}

export async function updateUserEmailCount(id: string, count: number, date: Date): Promise<void> {
  await db.update(users).set({ dailyEmailCount: count, lastEmailDate: date, updatedAt: new Date() }).where(eq(users.id, id));
}

// Session operations
export async function createSession(sessionData: Omit<NewSession, 'id' | 'createdAt'>): Promise<Session> {
  const [session] = await db.insert(sessions).values(sessionData).returning();
  return session;
}

export async function getSessionByToken(token: string): Promise<Session | null> {
  const [session] = await db.select().from(sessions).where(eq(sessions.token, token));
  return session || null;
}

export async function deleteSession(token: string): Promise<void> {
  await db.delete(sessions).where(eq(sessions.token, token));
}

export async function deleteExpiredSessions(): Promise<void> {
  await db.delete(sessions).where(lt(sessions.expiresAt, new Date()));
}

// OTP operations
export async function createOTPRequest(otpData: Omit<NewOtpRequest, 'id' | 'createdAt'>): Promise<OtpRequest> {
  const [otp] = await db.insert(otpRequests).values(otpData).returning();
  return otp;
}

export async function getOTPByRequestId(requestId: string): Promise<OtpRequest | null> {
  const [otp] = await db.select().from(otpRequests).where(
    and(
      eq(otpRequests.requestId, requestId),
      eq(otpRequests.isUsed, false),
      gte(otpRequests.expiresAt, new Date())
    )
  );
  return otp || null;
}

export async function incrementOTPAttempts(requestId: string): Promise<void> {
  await db.update(otpRequests)
    .set({ attempts: db.select({ attempts: otpRequests.attempts }).from(otpRequests).where(eq(otpRequests.requestId, requestId)) + 1 })
    .where(eq(otpRequests.requestId, requestId));
}

export async function markOTPAsUsed(requestId: string): Promise<void> {
  await db.update(otpRequests).set({ isUsed: true }).where(eq(otpRequests.requestId, requestId));
}

export async function getLatestOTPForEmail(email: string): Promise<OtpRequest | null> {
  const [otp] = await db.select().from(otpRequests)
    .where(eq(otpRequests.email, email))
    .orderBy(otpRequests.createdAt)
    .limit(1);
  return otp || null;
}

// Alias for consistency with forgot password API
export async function createOtpRequest(otpData: Omit<NewOtpRequest, 'id' | 'createdAt'>): Promise<OtpRequest> {
  return createOTPRequest(otpData);
}

export async function getOtpRequestByRequestId(requestId: string): Promise<OtpRequest | null> {
  return getOTPByRequestId(requestId);
}

export async function deleteOtpRequest(requestId: string): Promise<void> {
  await db.delete(otpRequests).where(eq(otpRequests.requestId, requestId));
}

// Email limit operations
export async function getEmailLimit(email: string): Promise<EmailLimit | null> {
  const [limit] = await db.select().from(emailLimits).where(eq(emailLimits.email, email));
  return limit || null;
}

export async function createOrUpdateEmailLimit(email: string): Promise<EmailLimit> {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const existing = await getEmailLimit(email);

  if (existing) {
    const lastRequestDate = new Date(existing.lastRequestDate);
    lastRequestDate.setHours(0, 0, 0, 0);

    if (lastRequestDate.getTime() === today.getTime()) {
      // Same day, increment count
      const [updated] = await db.update(emailLimits)
        .set({
          requestCount: existing.requestCount + 1,
          lastRequestDate: new Date()
        })
        .where(eq(emailLimits.email, email))
        .returning();
      return updated;
    } else {
      // New day, reset count
      const [updated] = await db.update(emailLimits)
        .set({
          requestCount: 1,
          lastRequestDate: new Date()
        })
        .where(eq(emailLimits.email, email))
        .returning();
      return updated;
    }
  } else {
    // Create new record
    const [created] = await db.insert(emailLimits)
      .values({
        email,
        requestCount: 1,
        lastRequestDate: new Date()
      })
      .returning();
    return created;
  }
}

export function generateRequestId(): string {
  return uuidv4();
}

// Email limit aliases for consistency
export async function getEmailLimitByEmail(email: string): Promise<EmailLimit | null> {
  return getEmailLimit(email);
}

export async function createEmailLimit(email: string, dailyCount: number, lastEmailDate: Date): Promise<EmailLimit> {
  const [created] = await db.insert(emailLimits)
    .values({
      email,
      requestCount: dailyCount,
      lastRequestDate: lastEmailDate
    })
    .returning();
  return created;
}

export async function updateEmailLimit(email: string, dailyCount: number, lastEmailDate: Date): Promise<EmailLimit> {
  const [updated] = await db.update(emailLimits)
    .set({
      requestCount: dailyCount,
      lastRequestDate: lastEmailDate
    })
    .where(eq(emailLimits.email, email))
    .returning();
  return updated;
}

// Office Flow Link operations
export async function createOfficeFlowLink(linkData: Omit<NewOfficeFlowLink, 'id' | 'createdAt' | 'updatedAt'>): Promise<OfficeFlowLink> {
  const [link] = await db.insert(officeFlowLinks).values({
    ...linkData,
    updatedAt: new Date()
  }).returning();
  return link;
}

export async function getOfficeFlowLinkByUserId(userId: string): Promise<OfficeFlowLink | null> {
  const [link] = await db.select().from(officeFlowLinks)
    .where(and(eq(officeFlowLinks.userId, userId), eq(officeFlowLinks.isActive, true)));
  return link || null;
}

export async function getOfficeFlowLinkByOfficeFlowUserId(officeFlowUserId: string): Promise<OfficeFlowLink | null> {
  const [link] = await db.select().from(officeFlowLinks)
    .where(and(eq(officeFlowLinks.officeFlowUserId, officeFlowUserId), eq(officeFlowLinks.isActive, true)));
  return link || null;
}

export async function getUserByOfficeFlowUserId(officeFlowUserId: string): Promise<User | null> {
  const result = await db.select({ user: users })
    .from(officeFlowLinks)
    .innerJoin(users, eq(officeFlowLinks.userId, users.id))
    .where(and(eq(officeFlowLinks.officeFlowUserId, officeFlowUserId), eq(officeFlowLinks.isActive, true)));

  return result[0]?.user || null;
}

export async function updateOfficeFlowLinkTokens(
  userId: string,
  accessToken: string,
  refreshToken?: string,
  expiresAt?: Date
): Promise<void> {
  await db.update(officeFlowLinks)
    .set({
      accessToken,
      refreshToken,
      tokenExpiresAt: expiresAt,
      updatedAt: new Date()
    })
    .where(and(eq(officeFlowLinks.userId, userId), eq(officeFlowLinks.isActive, true)));
}

export async function deactivateOfficeFlowLink(userId: string): Promise<void> {
  await db.update(officeFlowLinks)
    .set({
      isActive: false,
      updatedAt: new Date()
    })
    .where(eq(officeFlowLinks.userId, userId));
}

// Category operations
export async function createCategory(categoryData: Omit<NewCategory, 'id' | 'createdAt'>): Promise<Category> {
  const [category] = await db.insert(categories).values(categoryData).returning();
  return category;
}

export async function getCategoriesByUserId(userId: string): Promise<Category[]> {
  return await db.select().from(categories).where(eq(categories.userId, userId)).orderBy(asc(categories.name));
}

export async function getCategoryById(id: string, userId: string): Promise<Category | null> {
  const [category] = await db.select().from(categories).where(and(eq(categories.id, id), eq(categories.userId, userId)));
  return category || null;
}

export async function updateCategory(id: string, userId: string, updates: Partial<Pick<Category, 'name' | 'color'>>): Promise<void> {
  await db.update(categories).set(updates).where(and(eq(categories.id, id), eq(categories.userId, userId)));
}

export async function deleteCategory(id: string, userId: string): Promise<void> {
  await db.delete(categories).where(and(eq(categories.id, id), eq(categories.userId, userId)));
}

// Task operations
export async function createTask(taskData: Omit<NewTask, 'id' | 'createdAt' | 'updatedAt'>): Promise<Task> {
  const [task] = await db.insert(tasks).values(taskData).returning();
  return task;
}

export async function getTasksByUserId(userId: string, completed?: boolean): Promise<Task[]> {
  const conditions = [
    eq(tasks.userId, userId),
    eq(tasks.isRecurringTemplate, false) // Exclude recurring task templates
  ];
  if (completed !== undefined) {
    conditions.push(eq(tasks.completed, completed));
  }

  return await db.select().from(tasks)
    .where(and(...conditions))
    .orderBy(desc(tasks.createdAt)); // Sort by creation date, newest first
}

// Get all tasks including recurring instances for calendar view
export async function getTasksForCalendar(userId: string): Promise<Task[]> {
  const conditions = [
    eq(tasks.userId, userId),
    eq(tasks.isRecurringTemplate, false) // Exclude templates but include instances
  ];

  return await db.select().from(tasks)
    .where(and(...conditions))
    .orderBy(desc(tasks.createdAt));
}

export async function getTaskById(id: string, userId: string): Promise<Task | null> {
  const [task] = await db.select().from(tasks).where(and(eq(tasks.id, id), eq(tasks.userId, userId)));
  return task || null;
}

export async function updateTask(id: string, userId: string, updates: Partial<Omit<Task, 'id' | 'userId' | 'createdAt'>>): Promise<void> {
  await db.update(tasks).set({ ...updates, updatedAt: new Date() }).where(and(eq(tasks.id, id), eq(tasks.userId, userId)));
}

export async function deleteTask(id: string, userId: string): Promise<void> {
  await db.delete(tasks).where(and(eq(tasks.id, id), eq(tasks.userId, userId)));
}

export async function completeTask(id: string, userId: string): Promise<void> {
  await db.update(tasks).set({
    completed: true,
    completedAt: new Date(),
    updatedAt: new Date()
  }).where(and(eq(tasks.id, id), eq(tasks.userId, userId)));
}

export async function getTasksDueToday(userId: string): Promise<Task[]> {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  return await db.select().from(tasks)
    .where(and(
      eq(tasks.userId, userId),
      eq(tasks.completed, false),
      gte(tasks.dueDate, today),
      lt(tasks.dueDate, tomorrow)
    ))
    .orderBy(desc(tasks.priority), asc(tasks.dueDate));
}

export async function getOverdueTasks(userId: string): Promise<Task[]> {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  return await db.select().from(tasks)
    .where(and(
      eq(tasks.userId, userId),
      eq(tasks.completed, false),
      lt(tasks.dueDate, today)
    ))
    .orderBy(desc(tasks.priority), asc(tasks.dueDate));
}

// Email log operations
export async function createEmailLog(emailData: Omit<NewEmailLog, 'id' | 'sentAt'>): Promise<EmailLog> {
  const [emailLog] = await db.insert(emailLogs).values(emailData).returning();
  return emailLog;
}

export async function getEmailLogsByUserId(userId: string, limit: number = 10): Promise<EmailLog[]> {
  return await db.select().from(emailLogs)
    .where(eq(emailLogs.userId, userId))
    .orderBy(desc(emailLogs.sentAt))
    .limit(limit);
}

export async function getUserDailyEmailCount(userId: string, date: Date): Promise<number> {
  const startOfDay = new Date(date);
  startOfDay.setHours(0, 0, 0, 0);
  const endOfDay = new Date(date);
  endOfDay.setHours(23, 59, 59, 999);

  const result = await db.select().from(emailLogs)
    .where(and(
      eq(emailLogs.userId, userId),
      gte(emailLogs.sentAt, startOfDay),
      lt(emailLogs.sentAt, endOfDay)
    ));

  return result.length;
}
